/* 响应式设计工具类 - 优化标签管理界面的响应式设计 */

/* 标签管理容器响应式 */
.tag-management-container {
  @apply p-4 sm:p-6;
}

/* 标签管理头部响应式 */
.tag-management-header {
  @apply flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-6;
}

.tag-management-title {
  @apply text-xl sm:text-2xl font-bold text-foreground;
}

.tag-management-description {
  @apply text-sm sm:text-base text-muted-foreground mt-1;
}

.tag-management-actions {
  @apply flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-3 w-full sm:w-auto;
}

/* 工具栏响应式 */
.tag-toolbar {
  @apply flex flex-col lg:flex-row gap-4 mb-6;
}

.tag-search-container {
  @apply flex-1 relative min-w-0;
}

.tag-search-input {
  @apply w-full pl-10 pr-4 py-2 text-sm sm:text-base border border-input rounded-lg focus:ring-2 focus:ring-ring focus:border-transparent;
}

.tag-toolbar-controls {
  @apply flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-4;
}

.tag-sort-container {
  @apply flex items-center space-x-2 min-w-0;
}

.tag-sort-select {
  @apply px-2 sm:px-3 py-2 text-sm border border-input rounded-lg focus:ring-2 focus:ring-ring focus:border-transparent min-w-0;
}

/* 视图模式切换响应式 */
.tag-view-switcher {
  @apply flex items-center space-x-1 bg-muted rounded-lg p-1;
}

.tag-view-button {
  @apply p-1.5 sm:p-2 rounded-md transition-colors;
}

/* 标签网格响应式 - 优化后的固定尺寸布局 */
.tag-grid {
  @apply grid gap-4 sm:gap-6;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

@media (max-width: 640px) {
  .tag-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  .tag-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 1.25rem;
  }
}

@media (min-width: 1025px) {
  .tag-grid {
    grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
    gap: 1.5rem;
  }
}

/* 标签列表响应式 */
.tag-list {
  @apply space-y-3 sm:space-y-4;
}

/* 标签卡片响应式 - 优化后的固定布局 */
.tag-card {
  @apply p-3 sm:p-4 rounded-lg border-2 transition-all duration-200;
  min-height: 280px; /* 固定最小高度 */
}

.tag-card-header {
  @apply flex items-start justify-between mb-2 sm:mb-3;
}

.tag-card-icon-container {
  @apply flex-shrink-0 relative;
}

.tag-card-icon {
  @apply w-10 h-10 sm:w-12 sm:h-12 rounded-lg flex items-center justify-center;
}

.tag-card-content {
  @apply flex-1 min-w-0 ml-2 sm:ml-3;
}

.tag-card-title {
  @apply text-base sm:text-lg font-semibold text-foreground;
  /* 使用统一的固定标题样式 */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  min-height: 3rem;
  line-height: 1.5rem;
}

.tag-card-subtitle {
  @apply text-xs sm:text-sm text-muted-foreground mt-1;
}

.tag-card-actions {
  @apply flex-shrink-0 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-150;
}

.tag-card-action-button {
  @apply p-1.5 sm:p-2 text-muted-foreground hover:bg-accent rounded-lg transition-colors;
}

/* 分类卡片响应式 - 优化后的固定布局 */
.category-card {
  @apply p-3 sm:p-4 rounded-lg border-2 transition-all duration-200;
  min-height: 280px; /* 固定最小高度 */
}

.category-card-header {
  @apply flex items-start justify-between mb-2 sm:mb-3;
}

.category-card-icon-container {
  @apply flex-shrink-0 relative;
}

.category-card-icon {
  @apply w-10 h-10 sm:w-12 sm:h-12 rounded-lg flex items-center justify-center;
}

.category-card-content {
  @apply flex-1 min-w-0 ml-2 sm:ml-3;
}

.category-card-title {
  @apply text-base sm:text-lg font-semibold text-foreground;
  /* 使用统一的固定标题样式 */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  min-height: 3rem;
  line-height: 1.5rem;
}

.category-card-actions {
  @apply flex-shrink-0 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-150;
}

.category-card-action-button {
  @apply p-1.5 sm:p-2 text-muted-foreground hover:bg-accent rounded-lg transition-colors;
}

/* 标签统计信息响应式 */
.tag-stats-container {
  @apply bg-muted rounded-lg p-3 sm:p-4 mb-4 sm:mb-6;
}

.tag-stats-grid {
  @apply grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 text-center;
}

.tag-stats-item {
  @apply space-y-1;
}

.tag-stats-value {
  @apply text-lg sm:text-2xl font-bold;
}

.tag-stats-label {
  @apply text-xs sm:text-sm text-muted-foreground;
}

/* 批量操作栏响应式 */
.tag-batch-actions {
  @apply bg-card border border-border rounded-lg shadow-sm mb-4 sm:mb-6;
}

.tag-batch-actions-content {
  @apply p-3 sm:p-4;
}

.tag-batch-actions-header {
  @apply flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3;
}

.tag-batch-actions-info {
  @apply flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-3 min-w-0;
}

.tag-batch-actions-selected {
  @apply text-sm font-medium text-foreground whitespace-nowrap;
}

.tag-batch-actions-tags {
  @apply flex flex-wrap gap-1 min-w-0;
}

.tag-batch-actions-tag {
  @apply inline-flex items-center px-2 py-1 bg-secondary text-secondary-foreground text-xs rounded-full;
}

.tag-batch-actions-buttons {
  @apply flex flex-wrap items-center gap-2;
}

.tag-batch-action-button {
  @apply flex items-center px-2 sm:px-3 py-2 text-sm border rounded-lg transition-colors;
}

/* 标签云响应式 */
.tag-cloud-container {
  @apply p-4 sm:p-6 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg min-h-[250px] sm:min-h-[300px];
}

.tag-cloud-grid {
  @apply flex flex-wrap items-center justify-center gap-1 sm:gap-2;
}

.tag-cloud-item {
  @apply inline-flex items-center px-2 sm:px-3 py-1 rounded-full border transition-all duration-200 cursor-pointer transform hover:scale-105 active:scale-95;
}

.tag-cloud-legend {
  @apply mt-3 sm:mt-4 flex flex-wrap items-center justify-center gap-3 sm:gap-6 text-xs sm:text-sm text-muted-foreground;
}

.tag-cloud-legend-item {
  @apply flex items-center space-x-2;
}

.tag-cloud-legend-dot {
  @apply w-2 sm:w-3 h-2 sm:h-3 rounded-full;
}

/* 模态窗口响应式 */
.tag-modal-overlay {
  @apply fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50 p-4;
}

.tag-modal-container {
  @apply bg-card rounded-lg shadow-xl w-full max-w-md sm:max-w-lg max-h-[90vh] overflow-hidden;
}

.tag-modal-header {
  @apply px-4 sm:px-6 py-3 sm:py-4 border-b border-border;
}

.tag-modal-title {
  @apply text-lg sm:text-xl font-semibold text-foreground;
}

.tag-modal-content {
  @apply px-4 sm:px-6 py-3 sm:py-4 max-h-[60vh] overflow-y-auto;
}

.tag-modal-footer {
  @apply px-4 sm:px-6 py-3 sm:py-4 border-t border-border flex flex-col sm:flex-row items-stretch sm:items-center justify-end gap-2 sm:gap-3;
}

.tag-modal-button {
  @apply px-3 sm:px-4 py-2 text-sm font-medium rounded-lg transition-colors;
}

/* 通知响应式 */
.toast-container {
  @apply fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full;
}

@media (max-width: 640px) {
  .toast-container {
    @apply top-2 right-2 left-2 max-w-none;
  }
}

.toast-item {
  @apply flex items-start p-3 sm:p-4 rounded-lg shadow-lg border;
}

.toast-icon {
  @apply flex-shrink-0 w-4 h-4 sm:w-5 sm:h-5;
}

.toast-content {
  @apply ml-2 sm:ml-3 flex-1 min-w-0;
}

.toast-title {
  @apply text-sm font-semibold;
}

.toast-message {
  @apply mt-1 text-xs sm:text-sm opacity-90;
}

.toast-close {
  @apply ml-3 sm:ml-4 flex-shrink-0 text-muted-foreground hover:text-foreground transition-colors;
}

/* 加载指示器响应式 */
.loading-overlay {
  @apply fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50;
}

.loading-container {
  @apply bg-card rounded-lg shadow-lg max-w-xs sm:max-w-sm w-full mx-4;
}

.loading-content {
  @apply flex flex-col items-center justify-center p-4 sm:p-6;
}

.loading-spinner {
  @apply w-5 h-5 sm:w-6 sm:h-6 animate-spin text-primary-600;
}

.loading-message {
  @apply mt-2 text-sm sm:text-base text-muted-foreground text-center;
}

.loading-progress {
  @apply w-full bg-muted rounded-full h-2 mt-3;
}

.loading-progress-bar {
  @apply bg-primary-600 h-2 rounded-full transition-all duration-300 ease-out;
}

/* 空状态响应式 */
.empty-state {
  @apply text-center py-8 sm:py-12;
}

.empty-state-icon {
  @apply w-12 h-12 sm:w-16 sm:h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4;
}

.empty-state-title {
  @apply text-base sm:text-lg font-medium text-foreground mb-2;
}

.empty-state-description {
  @apply text-sm sm:text-base text-muted-foreground mb-4 sm:mb-6;
}

.empty-state-button {
  @apply inline-flex items-center px-3 sm:px-4 py-2 bg-primary-600 text-white text-sm rounded-lg hover:bg-primary-700 transition-colors;
}

/* 结果计数响应式 */
.result-count {
  @apply flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2 sm:gap-4 mb-3 sm:mb-4;
}

.result-count-info {
  @apply flex items-center space-x-2 text-xs sm:text-sm text-muted-foreground;
}

.result-count-clear {
  @apply text-xs sm:text-sm text-primary-600 hover:text-primary-700 font-medium;
}

/* 动画和过渡 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-in-right {
  animation: slideInRight 0.3s ease-in-out;
}

.slide-out-right {
  animation: slideOutRight 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* 打印样式 */
@media print {
  .tag-management-container {
    @apply p-0;
  }
  
  .tag-management-actions,
  .tag-toolbar,
  .tag-batch-actions,
  .tag-card-actions {
    @apply hidden;
  }
  
  .tag-card {
    @apply border border-border break-inside-avoid;
  }
  
  .tag-grid {
    @apply grid-cols-2;
  }
}