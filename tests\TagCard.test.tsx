// 标签卡片组件测试

import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import TagCard from '../src/components/TagCard'
import type { Tag } from '../src/types'

// 模拟ColorUtils
vi.mock('../src/utils/colorUtils', () => ({
  ColorUtils: {
    getContrastColor: vi.fn((color: string) => color === '#000000' ? '#ffffff' : '#000000')
  }
}))

describe('TagCard', () => {
  // 测试数据
  const mockTag: Tag & { usageCount: number } = {
    id: 'tag1',
    name: '技术',
    color: '#3B82F6',
    usageCount: 25,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-02')
  }

  const mockProps = {
    tag: mockTag,
    onEdit: vi.fn(),
    onDelete: vi.fn(),
    onClick: vi.fn()
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('基本渲染', () => {
    it('应该正确渲染标签信息', () => {
      render(<TagCard {...mockProps} />)

      expect(screen.getByText('技术')).toBeInTheDocument()
      expect(screen.getByText('25')).toBeInTheDocument()
      expect(screen.getByText('#3B82F6')).toBeInTheDocument() // 更新后的布局只显示颜色值
    })

    it('应该显示使用次数', () => {
      render(<TagCard {...mockProps} />)
      
      expect(screen.getByText('使用次数')).toBeInTheDocument()
      expect(screen.getByText('25')).toBeInTheDocument()
    })

    it('应该显示创建时间', () => {
      render(<TagCard {...mockProps} />)
      
      expect(screen.getByText('创建时间')).toBeInTheDocument()
    })

    it('应该显示更新时间（如果与创建时间不同）', () => {
      render(<TagCard {...mockProps} />)
      
      expect(screen.getByText('更新时间')).toBeInTheDocument()
    })

    it('应该显示更新时间占位（如果与创建时间相同）', () => {
      const tagWithSameTime = {
        ...mockTag,
        updatedAt: mockTag.createdAt
      }

      render(<TagCard {...mockProps} tag={tagWithSameTime} />)

      // 新布局中始终显示更新时间区域，但显示"暂无更新"
      expect(screen.getByText('更新时间')).toBeInTheDocument()
      expect(screen.getByText('暂无更新')).toBeInTheDocument()
    })
  })

  describe('颜色处理', () => {
    it('应该使用标签颜色作为背景', () => {
      const { container } = render(<TagCard {...mockProps} />)
      
      const card = container.firstChild as HTMLElement
      // 浏览器会将十六进制颜色转换为RGB格式
      expect(card.style.backgroundColor).toContain('59, 130, 246') // #3B82F6的RGB值
      expect(card.style.borderColor).toContain('59, 130, 246') // #3B82F6的RGB值
    })

    it('应该处理没有颜色的标签', () => {
      const tagWithoutColor = { ...mockTag, color: undefined }
      const { container } = render(<TagCard {...mockProps} tag={tagWithoutColor} />)
      
      const card = container.firstChild as HTMLElement
      // 浏览器会将十六进制颜色转换为RGB格式，更新为新的默认颜色
      expect(card.style.backgroundColor).toContain('248, 250, 252') // #F8FAFC的RGB值
      expect(card.style.borderColor).toContain('226, 232, 240') // #E2E8F0的RGB值
    })

    it('应该显示颜色预览', () => {
      render(<TagCard {...mockProps} />)
      
      expect(screen.getByText('#3B82F6')).toBeInTheDocument()
    })
  })

  describe('使用频率指示', () => {
    it('应该显示高频标签', () => {
      const highUsageTag = { ...mockTag, usageCount: 25 }
      render(<TagCard {...mockProps} tag={highUsageTag} />)
      
      expect(screen.getByText('高频')).toBeInTheDocument()
    })

    it('应该显示中频标签', () => {
      const mediumUsageTag = { ...mockTag, usageCount: 10 }
      render(<TagCard {...mockProps} tag={mediumUsageTag} />)
      
      expect(screen.getByText('中频')).toBeInTheDocument()
    })

    it('应该显示低频标签', () => {
      const lowUsageTag = { ...mockTag, usageCount: 3 }
      render(<TagCard {...mockProps} tag={lowUsageTag} />)
      
      expect(screen.getByText('低频')).toBeInTheDocument()
    })

    it('应该显示未使用标签', () => {
      const unusedTag = { ...mockTag, usageCount: 0 }
      render(<TagCard {...mockProps} tag={unusedTag} />)
      
      expect(screen.getByText('未使用')).toBeInTheDocument()
    })

    it('应该处理没有使用次数的标签', () => {
      const tagWithoutUsage = { ...mockTag }
      delete (tagWithoutUsage as any).usageCount
      
      render(<TagCard {...mockProps} tag={tagWithoutUsage} />)
      
      expect(screen.getByText('0')).toBeInTheDocument()
      expect(screen.getByText('未使用')).toBeInTheDocument()
    })
  })

  describe('交互功能', () => {
    it('应该处理卡片点击', () => {
      render(<TagCard {...mockProps} />)
      
      const card = screen.getByText('技术').closest('div')
      fireEvent.click(card!)
      
      expect(mockProps.onClick).toHaveBeenCalledTimes(1)
    })

    it('应该处理编辑按钮点击', () => {
      render(<TagCard {...mockProps} />)
      
      const editButton = screen.getByLabelText('编辑标签')
      fireEvent.click(editButton)
      
      expect(mockProps.onEdit).toHaveBeenCalledTimes(1)
      expect(mockProps.onClick).not.toHaveBeenCalled() // 不应该触发卡片点击
    })

    it('应该处理删除按钮点击', () => {
      render(<TagCard {...mockProps} />)
      
      const deleteButton = screen.getByLabelText('删除标签')
      fireEvent.click(deleteButton)
      
      expect(mockProps.onDelete).toHaveBeenCalledTimes(1)
      expect(mockProps.onClick).not.toHaveBeenCalled() // 不应该触发卡片点击
    })

    it('应该在没有onClick时不处理卡片点击', () => {
      const propsWithoutClick = { ...mockProps, onClick: undefined }
      render(<TagCard {...propsWithoutClick} />)
      
      const card = screen.getByText('技术').closest('div')
      fireEvent.click(card!)
      
      // 不应该抛出错误
    })
  })

  describe('时间格式化', () => {
    it('应该显示"今天"对于今天创建的标签', () => {
      const todayTag = { ...mockTag, createdAt: new Date() }
      render(<TagCard {...mockProps} tag={todayTag} />)
      
      expect(screen.getByText('今天')).toBeInTheDocument()
    })

    it('应该显示"昨天"对于昨天创建的标签', () => {
      const yesterday = new Date()
      yesterday.setDate(yesterday.getDate() - 1)
      const yesterdayTag = { ...mockTag, createdAt: yesterday }
      
      render(<TagCard {...mockProps} tag={yesterdayTag} />)
      
      expect(screen.getByText('昨天')).toBeInTheDocument()
    })

    it('应该显示天数对于一周内的标签', () => {
      const threeDaysAgo = new Date()
      threeDaysAgo.setDate(threeDaysAgo.getDate() - 3)
      const recentTag = { ...mockTag, createdAt: threeDaysAgo }
      
      render(<TagCard {...mockProps} tag={recentTag} />)
      
      expect(screen.getByText('3天前')).toBeInTheDocument()
    })

    it('应该处理无效日期', () => {
      const invalidDateTag = { ...mockTag, createdAt: new Date('invalid') }
      render(<TagCard {...mockProps} tag={invalidDateTag} />)
      
      expect(screen.getByText('未知时间')).toBeInTheDocument()
    })
  })

  describe('样式和类名', () => {
    it('应该应用自定义类名', () => {
      const { container } = render(<TagCard {...mockProps} className="custom-class" />)
      
      expect(container.firstChild).toHaveClass('custom-class')
    })

    it('应该有正确的基础样式类', () => {
      const { container } = render(<TagCard {...mockProps} />)
      
      const card = container.firstChild as HTMLElement
      expect(card).toHaveClass('group', 'relative', 'bg-white', 'border-2', 'rounded-lg')
    })

    it('应该在有onClick时添加悬停样式', () => {
      const { container } = render(<TagCard {...mockProps} />)
      
      const card = container.firstChild as HTMLElement
      expect(card).toHaveClass('hover:border-gray-300')
    })

    it('应该在没有onClick时不添加悬停样式', () => {
      const propsWithoutClick = { ...mockProps, onClick: undefined }
      const { container } = render(<TagCard {...propsWithoutClick} />)
      
      const card = container.firstChild as HTMLElement
      expect(card.className).not.toContain('hover:border-gray-300')
    })
  })

  describe('可访问性', () => {
    it('应该有正确的aria-label', () => {
      render(<TagCard {...mockProps} />)
      
      expect(screen.getByLabelText('编辑标签')).toBeInTheDocument()
      expect(screen.getByLabelText('删除标签')).toBeInTheDocument()
    })

    it('应该有正确的title属性', () => {
      render(<TagCard {...mockProps} />)
      
      const editButton = screen.getByLabelText('编辑标签')
      const deleteButton = screen.getByLabelText('删除标签')
      
      expect(editButton).toHaveAttribute('title', '编辑标签')
      expect(deleteButton).toHaveAttribute('title', '删除标签')
    })
  })

  describe('边界情况', () => {
    it('应该处理极长的标签名称', () => {
      const longNameTag = {
        ...mockTag,
        name: '这是一个非常非常非常长的标签名称，应该被正确截断显示'
      }

      render(<TagCard {...mockProps} tag={longNameTag} />)

      const nameElement = screen.getByText(longNameTag.name)
      // 新布局使用固定高度和line-clamp，检查元素是否存在
      expect(nameElement).toBeInTheDocument()
      expect(nameElement).toHaveClass('text-center')
    })

    it('应该处理空的标签名称', () => {
      const emptyNameTag = { ...mockTag, name: '' }
      render(<TagCard {...mockProps} tag={emptyNameTag} />)
      
      // 应该不抛出错误
    })

    it('应该处理负数使用次数', () => {
      const negativeUsageTag = { ...mockTag, usageCount: -1 }
      render(<TagCard {...mockProps} tag={negativeUsageTag} />)
      
      expect(screen.getByText('未使用')).toBeInTheDocument()
    })
  })
})