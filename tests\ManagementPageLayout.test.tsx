// ManagementPageLayout 组件测试

import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import { FolderTree, Tags } from 'lucide-react'
import ManagementPageLayout from '../src/components/ManagementPageLayout'

describe('ManagementPageLayout', () => {
  const defaultProps = {
    title: '测试管理',
    description: '这是一个测试页面',
    icon: FolderTree,
    children: <div>测试内容</div>
  }

  it('应该正确渲染基本布局', () => {
    render(<ManagementPageLayout {...defaultProps} />)
    
    expect(screen.getByText('测试管理')).toBeInTheDocument()
    expect(screen.getByText('这是一个测试页面')).toBeInTheDocument()
    expect(screen.getByText('测试内容')).toBeInTheDocument()
  })

  it('应该显示图标', () => {
    render(<ManagementPageLayout {...defaultProps} />)
    
    // 检查图标是否存在（通过SVG元素）
    const iconElement = document.querySelector('svg')
    expect(iconElement).toBeInTheDocument()
  })

  it('应该显示刷新按钮并处理点击', () => {
    const onRefresh = vi.fn()
    render(
      <ManagementPageLayout 
        {...defaultProps} 
        onRefresh={onRefresh}
      />
    )
    
    const refreshButton = screen.getByText('刷新')
    expect(refreshButton).toBeInTheDocument()
    
    fireEvent.click(refreshButton)
    expect(onRefresh).toHaveBeenCalledTimes(1)
  })

  it('应该显示新建按钮并处理点击', () => {
    const onCreate = vi.fn()
    render(
      <ManagementPageLayout 
        {...defaultProps} 
        onCreate={onCreate}
        createButtonText="新建项目"
      />
    )
    
    const createButton = screen.getByText('新建项目')
    expect(createButton).toBeInTheDocument()
    
    fireEvent.click(createButton)
    expect(onCreate).toHaveBeenCalledTimes(1)
  })

  it('应该在加载时禁用按钮', () => {
    const onRefresh = vi.fn()
    const onCreate = vi.fn()
    
    render(
      <ManagementPageLayout 
        {...defaultProps} 
        onRefresh={onRefresh}
        onCreate={onCreate}
        loading={true}
      />
    )
    
    const refreshButton = screen.getByText('刷新')
    const createButton = screen.getByText('新建')
    
    expect(refreshButton).toBeDisabled()
    expect(createButton).toBeDisabled()
  })

  it('应该显示额外的操作按钮', () => {
    const extraActions = <button>额外操作</button>
    
    render(
      <ManagementPageLayout 
        {...defaultProps} 
        extraActions={extraActions}
      />
    )
    
    expect(screen.getByText('额外操作')).toBeInTheDocument()
  })

  it('应该支持隐藏刷新和新建按钮', () => {
    render(
      <ManagementPageLayout 
        {...defaultProps} 
        showRefreshButton={false}
        showCreateButton={false}
        onRefresh={vi.fn()}
        onCreate={vi.fn()}
      />
    )
    
    expect(screen.queryByText('刷新')).not.toBeInTheDocument()
    expect(screen.queryByText('新建')).not.toBeInTheDocument()
  })

  it('应该应用自定义CSS类名', () => {
    const { container } = render(
      <ManagementPageLayout 
        {...defaultProps} 
        className="custom-class"
      />
    )
    
    expect(container.firstChild).toHaveClass('custom-class')
  })

  it('应该正确处理长标题', () => {
    const longTitle = '这是一个非常非常非常长的标题，应该被正确处理而不会破坏布局'
    
    render(
      <ManagementPageLayout 
        {...defaultProps} 
        title={longTitle}
      />
    )
    
    const titleElement = screen.getByText(longTitle)
    expect(titleElement).toBeInTheDocument()
    expect(titleElement).toHaveClass('truncate')
  })

  it('应该在加载时显示旋转动画', () => {
    render(
      <ManagementPageLayout 
        {...defaultProps} 
        onRefresh={vi.fn()}
        loading={true}
      />
    )
    
    // 检查是否有旋转动画类
    const refreshIcon = document.querySelector('.animate-spin')
    expect(refreshIcon).toBeInTheDocument()
  })
})
