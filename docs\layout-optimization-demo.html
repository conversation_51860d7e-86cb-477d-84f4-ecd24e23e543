<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标签管理和分类管理页面布局优化演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #2563eb;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #374151;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
            margin-top: 40px;
        }
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
            gap: 24px;
            margin: 20px 0;
        }
        .demo-card {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.2s;
            position: relative;
            min-height: 280px;
        }
        .demo-card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: #d1d5db;
        }
        .demo-card.colored {
            border-color: var(--card-border-color);
            background: var(--card-bg-color);
        }
        .card-actions {
            position: absolute;
            top: 8px;
            right: 8px;
            display: flex;
            gap: 4px;
            opacity: 0;
            transition: opacity 0.15s;
        }
        .demo-card:hover .card-actions {
            opacity: 1;
        }
        .action-btn {
            width: 28px;
            height: 28px;
            border: none;
            border-radius: 6px;
            background: #f3f4f6;
            color: #6b7280;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.15s;
        }
        .action-btn:hover {
            background: #dbeafe;
            color: #2563eb;
        }
        .icon-area {
            display: flex;
            justify-content: center;
            margin-bottom: 16px;
        }
        .icon-container {
            position: relative;
        }
        .icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }
        .usage-indicator {
            position: absolute;
            top: -4px;
            right: -4px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid white;
        }
        .title-area {
            margin-bottom: 16px;
        }
        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #111827;
            text-align: center;
            min-height: 56px;
            line-height: 28px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            word-break: break-word;
        }
        .description-area {
            margin-bottom: 16px;
            min-height: 40px;
        }
        .card-description {
            font-size: 14px;
            color: #6b7280;
            text-align: center;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            word-break: break-word;
        }
        .color-area {
            margin-bottom: 16px;
            display: flex;
            justify-content: center;
        }
        .color-display {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            background: #f9fafb;
            border-radius: 8px;
        }
        .color-dot {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 1px solid #d1d5db;
        }
        .color-text {
            font-size: 12px;
            color: #6b7280;
            font-family: monospace;
        }
        .stats-area {
            margin-bottom: 16px;
        }
        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
        }
        .stat-label {
            font-size: 14px;
            color: #6b7280;
        }
        .stat-value {
            font-size: 18px;
            font-weight: 600;
            color: #111827;
        }
        .divider {
            height: 1px;
            background: #e5e7eb;
            margin: 8px 0;
        }
        .time-info {
            min-height: 40px;
        }
        .time-item {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #6b7280;
            margin: 4px 0;
        }
        .bottom-area {
            display: flex;
            justify-content: center;
            min-height: 32px;
            align-items: center;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
        }
        .badge-high { background: #fef2f2; color: #dc2626; }
        .badge-medium { background: #fefce8; color: #ca8a04; }
        .badge-low { background: #f0fdf4; color: #16a34a; }
        .badge-active { background: #f0fdf4; color: #16a34a; }
        .badge-empty { background: #f3f4f6; color: #6b7280; }
        
        .feature-list {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .feature-list h3 {
            color: #1f2937;
            margin-top: 0;
        }
        .feature-list ul {
            margin: 0;
            padding-left: 20px;
        }
        .feature-list li {
            margin: 8px 0;
            color: #4b5563;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>标签管理和分类管理页面布局优化演示</h1>
        
        <div class="feature-list">
            <h3>🎯 优化目标</h3>
            <ul>
                <li><strong>图标和标题分行显示</strong>：图标在上方居中，标题独占一行</li>
                <li><strong>标题固定2行高度</strong>：每行最多20个中文字符，超出使用省略号截断</li>
                <li><strong>标签颜色独立显示</strong>：不与标题/图标挤在同一行，避免布局压缩</li>
                <li><strong>布局一致性</strong>：所有卡片具有相同尺寸和内部元素排列</li>
                <li><strong>固定占位空间</strong>：空数据字段显示占位内容，保持布局稳定</li>
            </ul>
        </div>

        <h2>📋 标签管理页面 - 优化后的网格视图</h2>
        <div class="demo-grid">
            <!-- 高频标签示例 -->
            <div class="demo-card colored" style="--card-bg-color: rgba(59, 130, 246, 0.03); --card-border-color: rgba(59, 130, 246, 0.2);">
                <div class="card-actions">
                    <button class="action-btn">✏️</button>
                    <button class="action-btn">🗑️</button>
                </div>
                <div class="icon-area">
                    <div class="icon-container">
                        <div class="icon" style="background-color: #3b82f6;">🏷️</div>
                        <div class="usage-indicator" style="background-color: #dc2626;"></div>
                    </div>
                </div>
                <div class="title-area">
                    <h3 class="card-title">前端开发技术</h3>
                </div>
                <div class="description-area"></div>
                <div class="color-area">
                    <div class="color-display">
                        <div class="color-dot" style="background-color: #3b82f6;"></div>
                        <span class="color-text">#3B82F6</span>
                    </div>
                </div>
                <div class="stats-area">
                    <div class="stat-item">
                        <span class="stat-label">使用次数</span>
                        <span class="stat-value">25</span>
                    </div>
                    <div class="divider"></div>
                    <div class="time-info">
                        <div class="time-item">
                            <span>创建时间</span>
                            <span>1月1日</span>
                        </div>
                        <div class="time-item">
                            <span>更新时间</span>
                            <span>1月15日</span>
                        </div>
                    </div>
                </div>
                <div class="bottom-area">
                    <span class="status-badge badge-high">高频</span>
                </div>
            </div>

            <!-- 长标题标签示例 -->
            <div class="demo-card colored" style="--card-bg-color: rgba(16, 185, 129, 0.03); --card-border-color: rgba(16, 185, 129, 0.2);">
                <div class="card-actions">
                    <button class="action-btn">✏️</button>
                    <button class="action-btn">🗑️</button>
                </div>
                <div class="icon-area">
                    <div class="icon-container">
                        <div class="icon" style="background-color: #10b981;">🏷️</div>
                        <div class="usage-indicator" style="background-color: #ca8a04;"></div>
                    </div>
                </div>
                <div class="title-area">
                    <h3 class="card-title">这是一个非常非常长的标签名称，用来演示标题的固定高度和截断效果</h3>
                </div>
                <div class="description-area"></div>
                <div class="color-area">
                    <div class="color-display">
                        <div class="color-dot" style="background-color: #10b981;"></div>
                        <span class="color-text">#10B981</span>
                    </div>
                </div>
                <div class="stats-area">
                    <div class="stat-item">
                        <span class="stat-label">使用次数</span>
                        <span class="stat-value">12</span>
                    </div>
                    <div class="divider"></div>
                    <div class="time-info">
                        <div class="time-item">
                            <span>创建时间</span>
                            <span>2天前</span>
                        </div>
                        <div class="time-item">
                            <span>更新时间</span>
                            <span>暂无更新</span>
                        </div>
                    </div>
                </div>
                <div class="bottom-area">
                    <span class="status-badge badge-medium">中频</span>
                </div>
            </div>

            <!-- 未使用标签示例 -->
            <div class="demo-card">
                <div class="card-actions">
                    <button class="action-btn">✏️</button>
                    <button class="action-btn">🗑️</button>
                </div>
                <div class="icon-area">
                    <div class="icon-container">
                        <div class="icon" style="background-color: #f3f4f6; color: #6b7280;">🏷️</div>
                        <div class="usage-indicator" style="background-color: #6b7280;"></div>
                    </div>
                </div>
                <div class="title-area">
                    <h3 class="card-title">待整理</h3>
                </div>
                <div class="description-area"></div>
                <div class="color-area">
                    <div class="color-display">
                        <div class="color-dot" style="background-color: #6b7280;"></div>
                        <span class="color-text">默认</span>
                    </div>
                </div>
                <div class="stats-area">
                    <div class="stat-item">
                        <span class="stat-label">使用次数</span>
                        <span class="stat-value">0</span>
                    </div>
                    <div class="divider"></div>
                    <div class="time-info">
                        <div class="time-item">
                            <span>创建时间</span>
                            <span>昨天</span>
                        </div>
                        <div class="time-item">
                            <span>更新时间</span>
                            <span>暂无更新</span>
                        </div>
                    </div>
                </div>
                <div class="bottom-area">
                    <span class="status-badge badge-low">未使用</span>
                </div>
            </div>
        </div>

        <h2>📁 分类管理页面 - 优化后的网格视图</h2>
        <div class="demo-grid">
            <!-- 活跃分类示例 -->
            <div class="demo-card colored" style="--card-bg-color: rgba(139, 92, 246, 0.03); --card-border-color: rgba(139, 92, 246, 0.2);">
                <div class="card-actions">
                    <button class="action-btn">✏️</button>
                    <button class="action-btn">🗑️</button>
                </div>
                <div class="icon-area">
                    <div class="icon-container">
                        <div class="icon" style="background-color: #8b5cf6;">📁</div>
                        <div class="usage-indicator" style="background-color: #8b5cf6;"></div>
                    </div>
                </div>
                <div class="title-area">
                    <h3 class="card-title">技术文档</h3>
                </div>
                <div class="description-area">
                    <p class="card-description">收集各种技术文档和学习资料</p>
                </div>
                <div class="color-area">
                    <div class="color-display">
                        <div class="color-dot" style="background-color: #8b5cf6;"></div>
                        <span class="color-text">#8B5CF6</span>
                    </div>
                </div>
                <div class="stats-area">
                    <div class="stat-item">
                        <span class="stat-label">书签数量</span>
                        <span class="stat-value">18</span>
                    </div>
                    <div class="divider"></div>
                    <div class="time-info">
                        <div class="time-item">
                            <span>创建时间</span>
                            <span>1月1日</span>
                        </div>
                        <div class="time-item">
                            <span>更新时间</span>
                            <span>1月10日</span>
                        </div>
                    </div>
                </div>
                <div class="bottom-area">
                    <span class="status-badge badge-active">活跃</span>
                </div>
            </div>

            <!-- 长标题和描述分类示例 -->
            <div class="demo-card colored" style="--card-bg-color: rgba(245, 158, 11, 0.03); --card-border-color: rgba(245, 158, 11, 0.2);">
                <div class="card-actions">
                    <button class="action-btn">✏️</button>
                    <button class="action-btn">🗑️</button>
                </div>
                <div class="icon-area">
                    <div class="icon-container">
                        <div class="icon" style="background-color: #f59e0b;">📁</div>
                        <div class="usage-indicator" style="background-color: #f59e0b;"></div>
                    </div>
                </div>
                <div class="title-area">
                    <h3 class="card-title">这是一个非常非常长的分类名称，用来演示标题的固定高度和截断效果</h3>
                </div>
                <div class="description-area">
                    <p class="card-description">这是一个非常详细的描述文本，用来演示描述区域的固定高度和截断效果，确保布局的一致性</p>
                </div>
                <div class="color-area">
                    <div class="color-display">
                        <div class="color-dot" style="background-color: #f59e0b;"></div>
                        <span class="color-text">#F59E0B</span>
                    </div>
                </div>
                <div class="stats-area">
                    <div class="stat-item">
                        <span class="stat-label">书签数量</span>
                        <span class="stat-value">5</span>
                    </div>
                    <div class="divider"></div>
                    <div class="time-info">
                        <div class="time-item">
                            <span>创建时间</span>
                            <span>3天前</span>
                        </div>
                        <div class="time-item">
                            <span>更新时间</span>
                            <span>暂无更新</span>
                        </div>
                    </div>
                </div>
                <div class="bottom-area">
                    <span class="status-badge badge-active">活跃</span>
                </div>
            </div>

            <!-- 空分类示例 -->
            <div class="demo-card">
                <div class="card-actions">
                    <button class="action-btn">✏️</button>
                    <button class="action-btn">🗑️</button>
                </div>
                <div class="icon-area">
                    <div class="icon-container">
                        <div class="icon" style="background-color: #f3f4f6; color: #6b7280;">📁</div>
                        <div class="usage-indicator" style="background-color: #6b7280;"></div>
                    </div>
                </div>
                <div class="title-area">
                    <h3 class="card-title">新建分类</h3>
                </div>
                <div class="description-area">
                    <p class="card-description"></p>
                </div>
                <div class="color-area">
                    <div class="color-display">
                        <div class="color-dot" style="background-color: #6b7280;"></div>
                        <span class="color-text">默认</span>
                    </div>
                </div>
                <div class="stats-area">
                    <div class="stat-item">
                        <span class="stat-label">书签数量</span>
                        <span class="stat-value">0</span>
                    </div>
                    <div class="divider"></div>
                    <div class="time-info">
                        <div class="time-item">
                            <span>创建时间</span>
                            <span>今天</span>
                        </div>
                        <div class="time-item">
                            <span>更新时间</span>
                            <span>暂无更新</span>
                        </div>
                    </div>
                </div>
                <div class="bottom-area">
                    <span class="status-badge badge-empty">空分类</span>
                </div>
            </div>
        </div>

        <div class="feature-list">
            <h3>✅ 优化成果</h3>
            <ul>
                <li><strong>视觉一致性</strong>：所有卡片具有相同的尺寸和内部元素排列</li>
                <li><strong>布局稳定性</strong>：固定高度确保无论数据多少都不会导致排版混乱</li>
                <li><strong>信息层次清晰</strong>：图标、标题、颜色、统计信息分层显示</li>
                <li><strong>响应式设计</strong>：在不同屏幕尺寸下保持良好的布局效果</li>
                <li><strong>用户体验提升</strong>：操作按钮悬停显示，交互反馈清晰</li>
            </ul>
        </div>
    </div>
</body>
</html>
