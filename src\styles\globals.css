@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式重置和基础样式 */
@layer base {
  * {
    box-sizing: border-box;
  }
  
  html {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
  
  body {
    margin: 0;
    padding: 0;
    line-height: 1.6;
    color: #1f2937;
    background-color: #ffffff;
  }
  
  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  ::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }
  
  ::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 262.1 83.3% 57.8%;
    --primary-foreground: 210 40% 98%;
    --secondary: 220 14.3% 95.9%;
    --secondary-foreground: 220.9 39.3% 11%;
    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;
    --accent: 220 14.3% 95.9%;
    --accent-foreground: 220.9 39.3% 11%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 262.1 83.3% 57.8%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.75rem;
  }
  .dark {
    --background: 224 71.4% 4.1%;
    --foreground: 210 20% 98%;
    --card: 224 71.4% 4.1%;
    --card-foreground: 210 20% 98%;
    --popover: 224 71.4% 4.1%;
    --popover-foreground: 210 20% 98%;
    --primary: 263.4 70% 50.4%;
    --primary-foreground: 210 20% 98%;
    --secondary: 215 27.9% 16.9%;
    --secondary-foreground: 210 20% 98%;
    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;
    --accent: 215 27.9% 16.9%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;
    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --ring: 263.4 70% 50.4%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

/* shadcn/ui 组件基础样式 */
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* shadcn/ui 组件样式扩展 */
@layer components {
  /* shadcn Button 组件扩展样式 */
  .shadcn-button-loading {
    @apply opacity-50 cursor-not-allowed;
  }
  
  .shadcn-button-loading::after {
    content: '';
    @apply inline-block w-4 h-4 ml-2 border-2 border-current border-t-transparent rounded-full animate-spin;
  }
  
  /* shadcn Input 组件扩展样式 */
  .shadcn-input-error {
    @apply border-destructive focus-visible:ring-destructive;
  }
  
  .shadcn-input-success {
    @apply border-green-500 focus-visible:ring-green-500;
  }
  
  /* shadcn Card 组件扩展样式 */
  .shadcn-card-hover {
    @apply transition-shadow duration-200 hover:shadow-md;
  }
  
  .shadcn-card-interactive {
    @apply cursor-pointer transition-all duration-200 hover:shadow-md hover:scale-[1.02];
  }
  
  /* shadcn Badge 组件扩展样式 */
  .shadcn-badge-dot {
    @apply relative;
  }
  
  .shadcn-badge-dot::before {
    content: '';
    @apply absolute -top-1 -right-1 w-2 h-2 bg-destructive rounded-full;
  }
  
  /* shadcn Dialog 组件扩展样式 */
  .shadcn-dialog-overlay {
    @apply fixed inset-0 z-50 bg-background/80 backdrop-blur-sm;
  }
  
  .shadcn-dialog-content {
    @apply fixed left-[50%] top-[50%] z-50 translate-x-[-50%] translate-y-[-50%];
  }
  
  /* shadcn Select 组件扩展样式 */
  .shadcn-select-content {
    @apply z-50 max-h-96 overflow-hidden;
  }
  
  /* shadcn Tooltip 组件扩展样式 */
  .shadcn-tooltip-content {
    @apply z-50 px-3 py-1.5 text-sm;
  }

/* 自定义组件样式 */
  /* 收藏管理页面头部布局稳定性样式 */
  .bookmark-header {
    @apply flex items-center justify-between mb-6;
    min-height: 64px; /* 固定最小高度 */
    position: relative;
  }
  
  .bookmark-header-title {
    @apply flex-shrink-0;
    min-width: 120px; /* 防止标题被压缩 */
  }
  
  .bookmark-header-controls {
    @apply flex items-center flex-wrap;
    min-height: 40px; /* 保持最小高度 */
    gap: 0.75rem; /* 使用gap替代space-x，在换行时表现更好 */
  }
  
  .bookmark-header-controls > * {
    @apply flex-shrink-0; /* 防止控制元素被压缩 */
  }
  
  /* ViewModeSelector 占位符样式 - 下拉框版本 */
  .view-mode-placeholder {
    @apply flex items-center justify-center border border-input rounded-lg bg-muted;
    min-width: 140px; /* 与下拉框宽度匹配 */
    height: 40px;
  }
  
  /* ViewModeSelector 容器样式 - 下拉框版本 */
  .view-mode-selector-container {
    @apply flex items-center flex-shrink-0;
    min-width: 140px; /* 确保容器有足够宽度 */
  }
  
  /* 主内容区域稳定宽度样式 */
  .main-content-stable {
    min-width: 800px; /* 设置最小宽度防止抖动 */
    width: 100%;
    transition: none; /* 禁用宽度过渡动画 */
  }
  
  /* 收藏管理内容区域稳定样式 */
  .bookmark-content-stable {
    min-width: 100%;
    width: 100%;
    overflow-x: auto; /* 内容过宽时显示滚动条而不是改变容器宽度 */
  }
  
  /* 按钮基础样式 */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
  }
  
  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80 focus:ring-ring;
  }
  
  .btn-outline {
    @apply border-input text-foreground hover:bg-accent focus:ring-ring;
  }
  
  /* 输入框基础样式 */
  .input {
    @apply block w-full px-3 py-2 border border-input rounded-lg text-sm placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring;
  }
  
  /* 卡片样式 */
  .card {
    @apply bg-card rounded-lg shadow-sm border border-border;
  }
  
  /* 标签样式 */
  .tag {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .tag-primary {
    @apply bg-primary-100 text-primary-800;
  }
  
  .tag-secondary {
    @apply bg-secondary text-secondary-foreground;
  }
  
  .tag-success {
    @apply bg-green-100 text-green-800;
  }
  
  .tag-warning {
    @apply bg-yellow-100 text-yellow-800;
  }
  
  .tag-danger {
    @apply bg-red-100 text-red-800;
  }
}

/* 工具类 */
@layer utilities {
  /* 文本截断 */
  .text-truncate {
    @apply truncate;
  }
  
  .text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .text-truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Tailwind line-clamp utilities */
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 文本换行和截断 */
  .break-words {
    word-break: break-word;
    overflow-wrap: break-word;
  }
  
  /* 容器宽度限制 */
  .container-constrained {
    max-width: 100%;
    overflow: hidden;
    word-wrap: break-word;
    word-break: break-word;
  }
  
  /* 标签容器样式 */
  .tags-container {
    @apply flex flex-wrap gap-1 max-w-full overflow-hidden;
  }
  
  .tag-item {
    @apply inline-flex items-center max-w-full;
    min-width: 0; /* 允许flex项目收缩 */
  }
  
  .tag-text {
    @apply truncate;
    max-width: 120px; /* 限制标签文字最大宽度 */
  }
  
  /* 动画 */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }
}

/* 动画关键帧 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 响应式设计辅助 */
@media (max-width: 640px) {
  .container-mobile {
    @apply px-4;
  }
}

/* 收藏管理页面头部响应式布局 */
@media (max-width: 768px) {
  .bookmark-header {
    @apply flex-col items-stretch;
    gap: 1rem;
    min-height: auto;
  }
  
  .bookmark-header-title {
    @apply text-center;
    min-width: auto;
  }
  
  .bookmark-header-controls {
    @apply justify-center;
    gap: 0.75rem;
  }
}

@media (max-width: 640px) {
  .bookmark-header-controls {
    @apply flex-col items-stretch;
    gap: 0.75rem;
  }
  
  .bookmark-header-controls > * {
    @apply w-full justify-center;
  }
  
  /* ViewModeSelector在小屏幕下的特殊处理 */
  .view-mode-selector-container {
    @apply w-full;
  }
  
  /* 搜索框在小屏幕下的调整 */
  .bookmark-header-controls input[type="text"] {
    width: 100% !important;
  }
  
  /* 确保select和button在小屏幕下正确显示 */
  .bookmark-header-controls select,
  .bookmark-header-controls button {
    @apply w-full;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .btn-primary {
    @apply border-2 border-primary-800;
  }
  
  .input {
    @apply border-2 border-input;
  }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}