// 分类管理主页面组件 - 分类管理的主容器

import React, { useState, useEffect, useCallback } from 'react'
import { AlertCircle, FolderTree } from 'lucide-react'
import ManagementPageLayout from './ManagementPageLayout'
import CategoryList from './CategoryList'
import CategoryModal from './CategoryModal'
import { categoryService } from '../services/categoryService'
import type { Category, CategoryInput, CategoryUpdate } from '../types'
import type { CategoryWithStats } from './CategoryList'

interface CategoryManagementTabProps {
  /** 自定义CSS类名 */
  className?: string
}

interface CategoryManagementState {
  categories: CategoryWithStats[]
  loading: boolean
  error: string | null
  showModal: boolean
  modalType: 'create' | 'edit' | 'delete'
  editingCategory: Category | null
  operationLoading: boolean
}

/**
 * 分类管理主页面组件
 * 作为分类管理的主容器，协调各个子组件的交互
 */
const CategoryManagementTab: React.FC<CategoryManagementTabProps> = React.memo(({
  className = ''
}) => {
  const [state, setState] = useState<CategoryManagementState>({
    categories: [],
    loading: true,
    error: null,
    showModal: false,
    modalType: 'create',
    editingCategory: null,
    operationLoading: false
  })

  // 加载分类数据
  const loadCategories = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }))
      
      // 获取所有分类及其统计信息
      const categoriesWithStats = await categoryService.getAllCategoriesWithStats()
      
      setState(prev => ({
        ...prev,
        categories: categoriesWithStats,
        loading: false
      }))
    } catch (error) {
      console.error('加载分类数据失败:', error)
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : '加载分类数据失败'
      }))
    }
  }, [])

  // 组件挂载时加载数据
  useEffect(() => {
    loadCategories()
  }, [loadCategories])

  // 处理创建分类
  const handleCreateCategory = useCallback(() => {
    setState(prev => ({
      ...prev,
      showModal: true,
      modalType: 'create',
      editingCategory: null
    }))
  }, [])

  // 处理编辑分类
  const handleEditCategory = useCallback((category: Category) => {
    setState(prev => ({
      ...prev,
      showModal: true,
      modalType: 'edit',
      editingCategory: category
    }))
  }, [])

  // 处理删除分类
  const handleDeleteCategory = useCallback((category: Category) => {
    setState(prev => ({
      ...prev,
      showModal: true,
      modalType: 'delete',
      editingCategory: category
    }))
  }, [])

  // 处理模态窗口关闭
  const handleModalClose = useCallback(() => {
    if (state.operationLoading) {
      return // 操作进行中时不允许关闭
    }
    
    setState(prev => ({
      ...prev,
      showModal: false,
      modalType: 'create',
      editingCategory: null
    }))
  }, [state.operationLoading])

  // 处理分类保存（创建或编辑）
  const handleCategorySave = useCallback(async (data: CategoryInput | CategoryUpdate) => {
    try {
      setState(prev => ({ ...prev, operationLoading: true }))

      let result: Category
      if (state.modalType === 'create') {
        result = await categoryService.createCategory(data as CategoryInput)
        console.log('分类创建成功:', result)
      } else if (state.modalType === 'edit' && state.editingCategory) {
        result = await categoryService.updateCategory(state.editingCategory.id, data as CategoryUpdate)
        console.log('分类更新成功:', result)
      } else {
        throw new Error('无效的操作类型')
      }

      // 重新加载分类数据
      await loadCategories()
      
      // 关闭模态窗口
      setState(prev => ({
        ...prev,
        showModal: false,
        modalType: 'create',
        editingCategory: null,
        operationLoading: false
      }))

      // 显示成功提示
      // TODO: 可以添加全局通知系统
      console.log(`分类${state.modalType === 'create' ? '创建' : '更新'}成功`)
      
    } catch (error) {
      console.error('保存分类失败:', error)
      setState(prev => ({ ...prev, operationLoading: false }))
      
      // 显示错误提示
      // TODO: 可以添加全局通知系统
      alert(`保存分类失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }, [state.modalType, state.editingCategory, loadCategories])

  // 处理分类删除
  const handleCategoryDelete = useCallback(async () => {
    if (!state.editingCategory) {
      return
    }

    try {
      setState(prev => ({ ...prev, operationLoading: true }))

      await categoryService.deleteCategory(state.editingCategory.id)
      console.log('分类删除成功:', state.editingCategory.name)

      // 重新加载分类数据
      await loadCategories()
      
      // 关闭模态窗口
      setState(prev => ({
        ...prev,
        showModal: false,
        modalType: 'create',
        editingCategory: null,
        operationLoading: false
      }))

      // 显示成功提示
      console.log('分类删除成功')
      
    } catch (error) {
      console.error('删除分类失败:', error)
      setState(prev => ({ ...prev, operationLoading: false }))
      
      // 显示错误提示
      alert(`删除分类失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }, [state.editingCategory, loadCategories])

  // 处理刷新
  const handleRefresh = useCallback(() => {
    loadCategories()
  }, [loadCategories])

  // 获取当前编辑分类的书签数量
  const getCurrentCategoryBookmarkCount = (): number => {
    if (!state.editingCategory) return 0
    const categoryWithStats = state.categories.find(c => c.id === state.editingCategory!.id)
    return categoryWithStats?.bookmarkCount || 0
  }

  // 渲染错误状态
  const renderErrorState = () => (
    <Card className="text-center py-12">
      <CardContent className="pt-6">
        <div className="w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center mx-auto mb-4">
          <AlertCircle className="w-8 h-8 text-destructive" />
        </div>
        <CardTitle className="text-lg mb-2">
          加载失败
        </CardTitle>
        <CardDescription className="mb-6">
          {state.error}
        </CardDescription>
        <Button onClick={handleRefresh} variant="default">
          <RefreshCw className="w-4 h-4 mr-2" />
          重试
        </Button>
      </CardContent>
    </Card>
  )

  return (
    <>
      <ManagementPageLayout
      title="分类管理"
      description="管理您的书签分类，更好地组织收藏内容"
      icon={FolderTree}
      className={className}
      loading={state.loading}
      onRefresh={handleRefresh}
      onCreate={handleCreateCategory}
      createButtonText="新建分类"
    >
      {state.error ? (
        renderErrorState()
      ) : (
        <CategoryList
          categories={state.categories}
          onCategoryEdit={handleEditCategory}
          onCategoryDelete={handleDeleteCategory}
          onCreateCategory={handleCreateCategory}
          loading={state.loading}
        />
      )}
      </ManagementPageLayout>

      {/* 分类模态窗口 */}
      <CategoryModal
        isOpen={state.showModal}
        type={state.modalType}
        category={state.editingCategory || undefined}
        bookmarkCount={getCurrentCategoryBookmarkCount()}
        onSave={state.modalType !== 'delete' ? handleCategorySave : undefined}
        onDelete={state.modalType === 'delete' ? handleCategoryDelete : undefined}
        onClose={handleModalClose}
        loading={state.operationLoading}
      />
    </>
  )
})

// 设置显示名称便于调试
CategoryManagementTab.displayName = 'CategoryManagementTab'

export default CategoryManagementTab

// 导出类型定义
export type { CategoryManagementTabProps }